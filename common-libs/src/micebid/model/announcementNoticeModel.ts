import { IPageRequest } from "../../basic";

export class IAnnouncementNoticeFilter extends IPageRequest {
    begin?:string
    end?:string
    isWindowShow?:boolean
    isWindow?:boolean
    effectScope?:number
    state?:string
}

export class IAnnouncementNotice {
    id?: number | null
    creator?: string
    createTime?: string
    updater?: string
    updateTime?: string
    title?: string
    effectScope?: number
    contentForm?: number
    informContent?: string
    sort?: number
    isWindow?: boolean
    name?: string
    code?: string
    state?: string
    description?: string
}