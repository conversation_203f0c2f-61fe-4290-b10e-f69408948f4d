
type keys = 'WAIT' | 'ALREADY' | 'ABANDON' | 'WON_THE_BID' | 'NOT_THE_BID'

export const BidPushState = {
  // 会议类型定义
  WAIT: { code: 10, desc: '待竞价' },
  ALREADY: { code: 20, desc: '已竞价' },
  ABANDON: { code: 30, desc: '放弃竞价' },
  WON_THE_BID: { code: 40, desc: '已中标' },
  NOT_THE_BID: { code: 50, desc: '未中标' },

  // 工具方法
  ofType: (type?: number): { code: number, desc: string } | null => {
    for (const key in BidPushState) {
      const item = BidPushState[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number, desc: string } | undefined)[] => {
    const types = Object.keys(BidPushState).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return BidPushState[i as keys]
      }
      return
    })
    const newTypes = types.filter(function (s) {
      return s && s;
    })
    return newTypes
  },
};