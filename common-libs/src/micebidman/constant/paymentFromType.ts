// 付款单状态枚举
export enum PaymentFromStatusEnum {
    PENDING_INVOICE_UPLOAD = 10, // 待上传发票
    CONFIRM_PAYMENT = 20,        // 确认付款
    COMPLETED = 30,              // 已完成
  }
  
  export const PaymentFromStatusMap = {
    [PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD]: '待上传发票',
    [PaymentFromStatusEnum.CONFIRM_PAYMENT]: '确认付款',
    [PaymentFromStatusEnum.COMPLETED]: '已完成',
  } as const;
  
  // 状态标签颜色映射
  export const PaymentFromStatusTagColorMap = {
    [PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD]: '#FF8133',
    [PaymentFromStatusEnum.CONFIRM_PAYMENT]: '#1989fa',
    [PaymentFromStatusEnum.COMPLETED]: '#52C41A',
  } as const;
  