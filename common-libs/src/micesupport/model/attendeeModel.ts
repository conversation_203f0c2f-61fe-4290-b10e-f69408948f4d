import dayjs, { Dayjs } from 'dayjs';
import { IPageRequest } from '../../basic';
import { UploadFile } from '../../micebid';

export class IMeetingAttendeeFilter extends IPageRequest {
  miceInfoId?: number;
  begin?: string;
  end?: string;
  miceId?: string;
  groupId?: string;
  createName?: string;
  billType?: number;
}

export class IMeetingAttendee {
  id?: number | null;
  miceCode?: number;
  miceInfoId?: number;
  miceInfoName?: string;
  enterpriseName?: string;
  enterpriseCode?: string;
  nickName?: string;
  userName?: string;
  phone?: string;
  isStay?: boolean;
  idCard?: number;
  tagName?: string | string[];
  companyName?: string;
  departName?: string;
  specialRequest?: string;
  remark?: string;
  isSeatSign?: boolean;
  isBreastPiece?: boolean;
  userSource?: string;
  approveState?: number;
}
export class IMeetingAttendeeFile {
  miceInfoId?: number;
  file?: string;
}

export class IMeetingDetails {
  id?: number;
  miceCode?: number;
  miceName?: string;
  miceSource?: number | null;
  miceStartDate?: string;
  miceEndDate?: string;
  miceHotelName?: string;
  miceHotelCode?: number[];
  miceConnectMeetingId?: string[];
  operatorCode?: string;
  operatorName?: string;
  consultantUserCode?: string;
  consultantUsername?: string;
  isSignUp?: number;
  signUpUrl?: string;
  aiOpen?: number;
  createTime?: string;
  updateTime?: string;
}

export class IAgendaFilter extends IPageRequest {
  id?: number | null;
  miceInfoId?: number;
  miceId?: number | null;
  miceInfoName?: string;
  agendaDate?: string;
  address?: string;
  startTime?: string;
  endTime?: string;
  subject?: string;
  speaker?: string;
  gmtCreate?: string;
  sort?: number;
}

export class IMeetingAgenda {
  id?: number | null;
  miceInfoId?: number;
  miceInfoName?: string;
  agendaDate?: Dayjs | string;
  address?: string;
  startTime?: Dayjs | string;
  endTime?: Dayjs | string;
  subject?: string;
  speaker?: string;
  gmtCreate?: string;
  sort?: number;
}

export class IMeetingAgendaDetails {
  id?: number;
  miceInfoId?: number;
  miceInfoName?: string;
  agendaDate?: string;
  address?: string;
  startTime?: string;
  endTime?: string;
  subject?: string;
  speaker?: string;
  gmtCreate?: string;
  sort?: number;
}
