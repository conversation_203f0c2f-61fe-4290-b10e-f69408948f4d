export class IPayHeader{
    applicationCode?: string
    nonce?: string
    timestamp?: string
    excludes?: string
    sign?: string

}

export interface IPayData {
    applicationCode?: string
    payTypes?: number[]
    username?: string
    enterpriseCode?: string
    orderCode?: string
    providerCode?: string
    amount?: string
    notifyUrl?: string
    callbackUrl?: string
    orderDetailsUrl?: string
    description?: string
    payload?: string
    hbTimestamp?: string
    hbNonce?: string
    sign?: string
    businessType?:string
    startApproveFlag?: boolean
    extJsonParam?: string
}

export interface IloginUser {
    nickName?: string,
    username?: string
    // 业务子编码
    businessType?: string
    processId?: string
    startApproveFlag?: string
    feeItemCode?: string
    feeItemName?: string

}