<script setup lang="ts">
// 方案互动-会场方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import {
  hotelLevelAllConstant,
  PlaceUsageTimeTypeConstant,
  UsagePurposeTypeConstant,
  TableTypeConstant,
  HotelsArr,
  LedSourceTypeConstant,
} from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  demandHotels: {
    type: Array,
    default: [],
  },
  hotels: {
    type: Array,
    default: [],
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
  merchantType: {
    type: Number,
    default: null,
  },
  readonly: {
    // 是否为只读模式
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemePlacesEmit']);

const oldSchemeList = ref<any[]>([]);
const newSchemeList = ref<any[]>([]);

const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

// 深拷贝函数
const deepClone = (obj: any): any => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map((item: any) => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

// 左侧数据（深拷贝）
const leftData = ref<any[]>([]);
// 右侧数据（深拷贝）
const rightData = ref<any[]>([]);

// 价格计算
const priceCalcFun = () => {
  const isAllPriceWrite = newSchemeList.value.every((e: any) => e.billUnitPlacePrice && e.billUnitPlacePrice > 0);
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e: any) => {
      // 会场单价
      subtotal.value += e.billUnitPlacePrice || 0;

      if (e.hasLed && e.billUnitLedPrice && e.billLedNum) {
        // LED单价*LED数量
        subtotal.value += e.billUnitLedPrice * e.billLedNum;
      }
      if (e.hasTea && e.billUnitTeaPrice && e.billPersonNum) {
        // 茶歇单价*会场人数
        subtotal.value += e.billUnitTeaPrice * e.billPersonNum;
      }
    });

    emit('schemePriceEmit', { type: 'place', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  }
};

watch(
  () => props.schemeItem,
  (newObj: any) => {
    oldSchemeList.value = JSON.parse(JSON.stringify(newObj))?.places || [];

    // 使用深拷贝创建左边的原始数据
    leftData.value = deepClone(oldSchemeList.value);
    rightData.value = deepClone(oldSchemeList.value);

    if (props.isSchemeCache && props.schemeCacheItem && !props.readonly) {
      // 编辑模式：使用缓存数据
      newSchemeList.value = props.schemeCacheItem?.places || [];

      // 确保缓存数据中的字段正确映射
      newSchemeList.value.forEach((item: any) => {
        // 从原始数据中查找对应的详情数据
        const originalPlace = oldSchemeList.value.find(
          (orig: any) => orig.miceDemandPlaceId === item.miceDemandPlaceId || orig.id === item.miceDemandPlaceId,
        );

        if (originalPlace) {
          // 更新 guildhall 字段
          if (!item.schemeGuildhall && !item.billGuildhall && originalPlace.guildhall) {
            item.schemeGuildhall = originalPlace.guildhall;
            item.billGuildhall = originalPlace.guildhall;
          }

          // 确保 miceSchemeHotelId 使用详情数据中的最新值
          if (originalPlace.miceSchemeHotelId) {
            item.miceSchemeHotelId = originalPlace.miceSchemeHotelId;
          }

          // 确保 miceSchemePlaceId 使用详情数据中的id
          if (originalPlace.id) {
            item.miceSchemePlaceId = originalPlace.id;
          }

          // 确保方案价格字段使用详情数据中的最新值（方案数据不应该被修改）
          if (originalPlace.schemeUnitLedPrice !== undefined) {
            item.schemeUnitLedPrice = originalPlace.schemeUnitLedPrice;
          }
          if (originalPlace.schemeUnitPlacePrice !== undefined) {
            item.schemeUnitPlacePrice = originalPlace.schemeUnitPlacePrice;
          }
          if (originalPlace.schemeUnitTeaPrice !== undefined) {
            item.schemeUnitTeaPrice = originalPlace.schemeUnitTeaPrice;
          }

          // 确保 sourceId 使用详情数据中的最新值
          if (originalPlace.sourceId !== undefined) {
            item.sourceId = originalPlace.sourceId;
          }
        }

        // 确保缓存数据中的 billLedSource 使用 ledSpecs 的值
        if (item.ledSpecs) {
          item.billLedSource = item.ledSpecs;
        }

        // 确保缓存数据中的 billUnitTeaPrice 使用 schemeUnitTeaPrice 的值
        if (item.schemeUnitTeaPrice) {
          item.billUnitTeaPrice = item.schemeUnitTeaPrice;
        }
      });

      // 价格计算
      priceCalcFun();
    } else {
      const demandPlaces = JSON.parse(JSON.stringify(newObj))?.places || [];

      newSchemeList.value = demandPlaces.map((e: any) => {
        return {
          // 基础字段
          tempSchemeHotelId:
            props.hotels && (props.hotels as any[]).length === 1 ? (props.hotels as any[])[0].tempId : null,
          miceDemandPushHotelId:
            props.hotels && (props.hotels as any[]).length === 1
              ? (props.hotels as any[])[0].miceDemandPushHotelId
              : null,
          miceDemandHotelId: e.miceDemandHotelId,
          miceDemandPlaceId: e.miceDemandPlaceId,
          miceSchemeHotelId: e.miceSchemeHotelId,
          miceSchemePlaceId: e.id, // 方案会场id - 使用详情中的id

          // 日期和时间
          demandDate: e.demandDate,
          usageTime: e.usageTime,
          usagePurpose: e.usagePurpose,

          // 人数和面积
          schemePersonNum: e.personNum,
          // 🔥 查看模式使用详情数据，编辑模式使用默认值
          billPersonNum: props.readonly ? e.billPersonNum || e.personNum : e.personNum,
          area: e.area,
          underLightFloor: e.underLightFloor,
          tableType: e.tableType,

          // LED相关
          hasLed: e.hasLed,
          schemeLedNum: e.ledNum,
          schemeLedSource: e.schemeLedSource || null,
          ledSpecs: e.ledSpecs,
          // 🔥 查看模式使用详情数据，编辑模式使用默认值
          billLedNum: props.readonly ? e.billLedNum || e.ledNum : e.ledNum,
          billLedSource: e.ledSpecs || null, // 账单LED来源，从详情数据的ledSpecs字段获取

          // 茶歇相关
          hasTea: e.hasTea,
          teaEachTotalPrice: e.teaEachTotalPrice,
          teaDesc: e.teaDesc,

          // 会议厅
          schemeGuildhall: e.guildhall || e.schemeGuildhall || null, // 方案会议厅
          // 🔥 查看模式使用详情数据，编辑模式使用默认值
          billGuildhall: props.readonly
            ? e.billGuildhall || e.guildhall || e.schemeGuildhall
            : e.guildhall || e.schemeGuildhall || null,

          // 价格相关
          schemeUnitPlacePrice: e.schemeUnitPlacePrice || null, // 方案报价会场单价
          // 🔥 查看模式使用详情数据，编辑模式使用默认值
          billUnitPlacePrice: props.readonly ? e.billUnitPlacePrice || null : null,
          schemeUnitLedPrice: e.schemeUnitLedPrice || null, // 方案报价LED单价
          // 🔥 查看模式使用详情数据，编辑模式使用默认值
          billUnitLedPrice: props.readonly ? e.billUnitLedPrice || null : null,
          schemeUnitTeaPrice: e.teaEachTotalPrice, // 方案报价茶歇单价
          // 🔥 查看模式使用详情数据，编辑模式使用默认值
          billUnitTeaPrice: props.readonly ? e.billUnitTeaPrice || e.schemeUnitTeaPrice : e.schemeUnitTeaPrice || null,

          // 市场价和协议价
          msMarketPriceInquiryDetailsId: e.msMarketPriceInquiryDetailsId,
          marketPriceUnitPrice: e.marketPriceUnitPrice,
          agreementProductId: e.agreementProductId,
          agreementUnitPrice: e.agreementUnitPrice,
          retailUnitPrice: e.retailUnitPrice,

          // 其他字段
          description: e.description,
          sourceId: e.sourceId,
          // 🔥 查看模式使用详情数据，编辑模式使用默认值
          invoiceTempId: props.readonly ? e.invoiceTempId || undefined : undefined,
          statementTempId: props.readonly ? e.statementTempId || undefined : undefined,
        };
      });

    }

    // 临时解决方案：如果 newSchemeList 为空但 leftData 有数据，则使用 leftData 初始化 newSchemeList
    if (newSchemeList.value.length === 0 && leftData.value.length > 0) {
      newSchemeList.value = leftData.value.map((item: any) => ({
        // 保持原有字段
        ...item,
        // 添加方案相关字段
        tempSchemeHotelId:
          props.hotels && (props.hotels as any[]).length === 1
            ? (props.hotels as any[])[0].tempId
            : item.tempSchemeHotelId || null,
        miceDemandPushHotelId:
          props.hotels && (props.hotels as any[]).length === 1
            ? (props.hotels as any[])[0].miceDemandPushHotelId
            : item.miceDemandPushHotelId || null,
        miceDemandPlaceId: item.id,
        // 确保人数字段正确
        schemePersonNum: item.personNum || item.schemePersonNum,
        billPersonNum: item.personNum || item.schemePersonNum, // 账单人数，默认等于方案人数
        // 确保LED相关字段正确
        schemeLedNum: item.ledNum || item.schemeLedNum,
        billLedNum: item.ledNum || item.schemeLedNum, // 账单LED数量，默认等于方案LED数量
        schemeLedSource: item.schemeLedSource || null,
        billLedSource: item.ledSpecs || null, // 账单LED来源，从详情数据的ledSpecs字段获取
        // 会议厅
        schemeGuildhall: item.guildhall || item.schemeGuildhall || null, // 方案会议厅
        billGuildhall: item.guildhall || item.schemeGuildhall || null, // 账单会议厅，默认等于方案会议厅
        // 价格相关字段
        schemeUnitPlacePrice: item.schemeUnitPlacePrice || null,
        billUnitPlacePrice: null, // 账单报价会场单价
        schemeUnitLedPrice: item.schemeUnitLedPrice || null,
        billUnitLedPrice: null, // 账单报价LED单价
        schemeUnitTeaPrice: item.teaEachTotalPrice || item.schemeUnitTeaPrice,
        billUnitTeaPrice: item.schemeUnitTeaPrice || null, // 账单报价茶歇单价，从详情数据的schemeUnitTeaPrice字段获取
        // 确保酒店ID字段正确（用于酒店名称显示）
        miceSchemeHotelId: item.miceSchemeHotelId || item.miceDemandHotelId,
      }));
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

watch(
  () => [props.hotels, props.merchantType, newSchemeList.value],
  () => {
    newSchemeList.value.forEach((e: any) => {
      if (props.hotels && (props.hotels as any[]).length === 1) {
        // 是否直签酒店
        e.tempSchemeHotelId =
          props.merchantType !== 1
            ? (props.hotels as any[])[0].tempId
            : e.miceSchemeHotelId || (props.hotels as any[])[0].tempId;
        e.miceDemandPushHotelId = (props.hotels as any[])[0].miceDemandPushHotelId;
      }
    });
  },
  {
    immediate: true,
    deep: true,
  },
);

// 注释掉同步逻辑 - 左右两边数据应该互不影响
// 左边显示原始需求数据，右边显示可编辑的方案数据

const schemePlanLabelList = [
  '酒店选择',
  '会议厅选择',
  '使用时间',
  '会场用途',
  '人数',
  '摆台形式',
  '面积',
  '层高',
  'LED数量',
  'LED单价',
  'LED来源',
  'LED规格描述',
  '茶歇标准',
  '茶歇说明',
  '备注',
];

// 酒店名称
const hotelName = (hotelItem: any) => {
  let str = '-';

  // 🔥 优先使用demandHotels中的真实酒店名称
  if (props.demandHotels && props.demandHotels.length > 0) {
    // 通过ID匹配需求酒店
    const demandHotel = (props.demandHotels as any[]).find((e: any) => 
      e.id === hotelItem.miceSchemeHotelId ||
      e.miceDemandPushHotelId === hotelItem.miceDemandPushHotelId
    );
    if (demandHotel && demandHotel.hotelName) {
      str = demandHotel.hotelName;
      return str;
    }
  }

  // 备用方案：从props.hotels中查找（用于显示选中的酒店）
  if (hotelItem.tempSchemeHotelId && props.hotels) {
    (props.hotels as any[]).forEach((hotel: any, index: number) => {
      if (hotel.tempId === hotelItem.tempSchemeHotelId) {
        str = hotel.hotelName || '-';
      }
    });
  }

  // 兜底逻辑：显示城市区域信息
  if (str === '-' && props.demandHotels) {
    (props.demandHotels as any[]).forEach((e: any, index: number) => {
      if (e.id && e.id === hotelItem.miceSchemeHotelId) {
        // 优先显示酒店名称，如果没有则显示城市区域信息
        str = e.hotelName || `${e.cityName + e.districtName}/${(hotelLevelAllConstant.ofType(e.level)?.desc || '-')}`;
      }
    });
  }

  return str;
};

// 右边酒店名称显示（用于方案编辑区域）
const rightHotelName = (hotelItem: any) => {
  let str = '-';

  // 🔥 优先使用demandHotels中的真实酒店名称
  if (props.demandHotels && props.demandHotels.length > 0) {
    // 通过ID匹配需求酒店
    const demandHotel = (props.demandHotels as any[]).find((e: any) => 
      e.id === hotelItem.miceSchemeHotelId ||
      e.id === hotelItem.miceDemandHotelId ||
      e.miceDemandPushHotelId === hotelItem.miceDemandPushHotelId
    );
    if (demandHotel && demandHotel.hotelName) {
      str = demandHotel.hotelName;
      return str;
    }
  }

  // 备用方案：从props.hotels中查找选中的酒店
  if (hotelItem.tempSchemeHotelId && props.hotels) {
    (props.hotels as any[]).forEach((hotel: any, index: number) => {
      if (hotel.tempId === hotelItem.tempSchemeHotelId) {
        str = hotel.hotelName || '-';
      }
    });
  }

  // 兜底逻辑：显示城市区域信息
  if (str === '-' && props.demandHotels) {
    (props.demandHotels as any[]).forEach((e: any, index: number) => {
      if (e.id && (e.id === hotelItem.miceSchemeHotelId || e.id === hotelItem.miceDemandHotelId)) {
        // 优先显示酒店名称，如果没有则显示城市区域信息
        str = e.hotelName || `${e.cityName + e.districtName}/${(hotelLevelAllConstant.ofType(e.level)?.desc || '-')}`;
      }
    });
  }

  return str;
};

const changePrice = (index: number) => {
  // 价格计算
  priceCalcFun();
};

// 锚点
const anchorJump = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};

// 暂存
const placeTempSave = () => {
  emit('schemePlacesEmit', {
    schemePlaces: [...newSchemeList.value],
    schemeIndex: props.schemeIndex,
  });
};

// 校验
const placeSub = () => {
  let isPlaceVerPassed = true;

  newSchemeList.value.forEach((e: any, i: number) => {
    isVerifyFailed.value = true;

    if (isPlaceVerPassed === false) return;

    if (e.hasLed && (e.billUnitLedPrice === null || e.billUnitLedPrice === undefined)) {
      message.error('请填写' + e.demandDate + '会场' + (i + 1) + 'LED单价');

      isPlaceVerPassed = false;
      anchorJump('schemePlaceId' + e.demandDate + i);
      return;
    }

    if (!e.billUnitPlacePrice) {
      message.error('请输入' + e.demandDate + '会场' + (i + 1) + '账单单价');

      isPlaceVerPassed = false;
      anchorJump('schemePlaceId' + e.demandDate + i);
      return;
    }
  });

  if (isPlaceVerPassed) {
    placeTempSave();
  }

  return isPlaceVerPassed;
};

defineExpose({ placeSub, placeTempSave });

onMounted(async () => {
});
</script>

<template>
  <!-- 会场方案 -->
  <div class="scheme_place">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="leftData.length > 0">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>会场方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in leftData" :key="idx">
          <div class="scheme_plan_index">
            {{ '会场' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ hotelName(item) }}
                </template>
                {{ hotelName(item) }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">{{ item.guildhall }}</div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
                </template>
                {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
                </template>
                {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
                </template>
                {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.area ? item.area + '㎡' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.underLightFloor ? item.underLightFloor + 'm' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.ledNum || item.schemeLedNum || '-' }}
            </div>
            <div class="scheme_plan_value pl12">{{ item.schemeUnitLedPrice || '-' }}</div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ LedSourceTypeConstant.ofType(item.schemeLedSource)?.desc || '-' }}
                </template>
                {{ LedSourceTypeConstant.ofType(item.schemeLedSource)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.ledSpecs || '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.teaEachTotalPrice ? item.teaEachTotalPrice + '元/位' : '-' }}
            </div>
            <div class="scheme_plan_value pl12">
              {{ item.teaDesc || '-' }}
            </div>
            <div class="scheme_plan_value pl12">-</div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div class="scheme_plan_price_value">
                {{ item.schemeUnitPlacePrice ? '¥' + formatNumberThousands(item.schemeUnitPlacePrice) : '¥0.00' }}
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeUnitPlacePrice
                    ? '¥' +
                      formatNumberThousands(
                        item.schemeUnitPlacePrice +
                          (item.hasLed ? (item.schemeUnitLedPrice || 0) * item.schemeLedNum : 0) +
                          (item.hasTea ? item.teaEachTotalPrice * item.schemePersonNum : 0),
                      )
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPlacePrice">
                <div>
                  {{ item.schemeUnitPlacePrice + '(会场价格)' }}
                </div>
                <div v-if="item.hasLed && item.schemeUnitLedPrice">
                  {{ item.schemeLedNum + '(数量)*' + item.schemeUnitLedPrice + '(LED单价)' }}
                </div>
                <!-- <div v-if="item.hasTea">
                  {{ item.schemePersonNum + '(人数)*' + item.teaEachTotalPrice + '(茶歇单价)' }}
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>
      <!-- 右侧 -->
      <div class="common_table_r">
        <!-- 右侧会场方案 -->
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>会场账单</span>
        </div>
        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '会场' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pl12">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ rightHotelName(item) }}
                  </template>
                  {{ rightHotelName(item) }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pl12">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.billGuildhall || item.schemeGuildhall || item.guildhall || '-' }}
                  </template>
                  {{ item.billGuildhall || item.schemeGuildhall || item.guildhall || '-' }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pl12">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
                  </template>
                  {{ PlaceUsageTimeTypeConstant.ofType(item.usageTime)?.desc || '-' }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pl12">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
                  </template>
                  {{ UsagePurposeTypeConstant.ofType(item.usagePurpose)?.desc || '-' }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pl12">
                {{
                  item.billPersonNum || item.schemePersonNum ? (item.billPersonNum || item.schemePersonNum) + '人' : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pl12">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
                  </template>
                  {{ TableTypeConstant.ofType(item.tableType)?.desc || '-' }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pl12">
                {{ item.area ? item.area + '㎡' : '-' }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pl12">
                {{ item.underLightFloor ? item.underLightFloor + 'm' : '-' }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.billLedNum ? 'error_tip' : '']">
                <div
                  class="pl12"
                  v-if="
                    props.readonly ||
                    props.schemeType === 'biddingView' ||
                    props.schemeType === 'schemeView' ||
                    !item.hasLed
                  "
                >
                  {{ item.billLedNum || item.schemeLedNum || '-' }}
                </div>
                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.billLedNum"
                    @blur="changePrice(idx)"
                    placeholder="LED数量"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="99999"
                  />
                  <span>个</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.schemeUnitLedPrice === null || item.schemeUnitLedPrice === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    props.readonly ||
                    props.schemeType === 'biddingView' ||
                    props.schemeType === 'schemeView' ||
                    !item.hasLed
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.billUnitLedPrice || item.schemeUnitLedPrice || '-' }}
                    </template>
                    {{ item.billUnitLedPrice || item.schemeUnitLedPrice || '-' }}
                  </a-tooltip>
                </div>

                <div class="pl12" v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.billUnitLedPrice"
                    @blur="changePrice(idx)"
                    placeholder="LED单价"
                    :bordered="false"
                    allow-clear
                    :min="0"
                    :max="99999"
                  />
                  <span>元</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && (item.schemeLedSource === null || item.schemeLedSource === undefined)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <div class="pl12">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ LedSourceTypeConstant.ofType(item.schemeLedSource)?.desc || '-' }}
                    </template>
                    {{ LedSourceTypeConstant.ofType(item.schemeLedSource)?.desc || '-' }}
                  </a-tooltip>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                {{ item.ledSpecs || '-' }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                {{
                  item.billUnitTeaPrice || item.teaEachTotalPrice || item.schemeUnitTeaPrice
                    ? (item.billUnitTeaPrice || item.teaEachTotalPrice || item.schemeUnitTeaPrice) + '元/位'
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border">
                {{ item.teaDesc || '-' }}
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pl12">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.description || '-' }}
                  </template>
                  {{ item.description || '-' }}
                </a-tooltip>
              </div>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12" :id="'schemePlaceId' + item.demandDate + idx">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div
                class="scheme_plan_price_value"
                v-if="props.readonly || props.schemeType === 'biddingView' || props.schemeType === 'schemeView'"
              >
                {{ item.billUnitPlacePrice || item.schemeUnitPlacePrice ? '¥' + formatNumberThousands(item.billUnitPlacePrice || item.schemeUnitPlacePrice) : '¥0.00' }}
              </div>

              <div
                :class="[
                  'scheme_plan_price_value',
                  isVerifyFailed && !item.billUnitPlacePrice ? 'error_price_tip' : '',
                ]"
                v-else
              >
                <a-input-number
                  v-model:value="item.billUnitPlacePrice"
                  @change="changePrice(idx)"
                  placeholder=""
                  :bordered="false"
                  :controls="false"
                  :min="0.01"
                  :max="999999.99"
                  :precision="2"
                  style="width: 100%"
                  allow-clear
                />
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  '¥' +
                  (item.billUnitPlacePrice
                    ? formatNumberThousands(
                        item.billUnitPlacePrice +
                          (item.hasLed ? (item.billUnitLedPrice || 0) * (item.billLedNum || 0) : 0) +
                          (item.hasTea ? (item.billUnitTeaPrice || 0) * (item.billPersonNum || 0) : 0),
                      )
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.billUnitPlacePrice">
                <div>
                  {{ item.billUnitPlacePrice + '(会场价格)' }}
                </div>
                <div v-if="item.hasLed && item.billUnitLedPrice">
                  {{ (item.billLedNum || 0) + '(数量)*' + item.billUnitLedPrice + '(LED单价)' }}
                </div>
                <!-- <div v-if="item.hasTea && item.billUnitTeaPrice">
                  {{ (item.billPersonNum || 0) + '(人数)*' + item.billUnitTeaPrice + '(茶歇单价)' }}
                </div> -->
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_place {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_place.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  .scheme_plan_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      padding: 0;
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }

  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f;
    }
  }

  .p0 {
    padding: 0 !important;
  }
  .pr0 {
    padding-right: 0 !important;
  }

  /* custom-antd.css */
  :deep(.ant-select-disabled .ant-select-selector) {
    color: rgba(134, 144, 156, 1);
  }
}
</style>
