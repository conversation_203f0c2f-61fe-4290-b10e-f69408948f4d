<script setup lang="ts">
import EMenuItem from './EMenuItem.vue';
import { BreadcrumbItem as hBreadcrumbItem, Breadcrumb as hBreadcrumb, LayoutSider as hLayoutSider, Layout<PERSON>ooter as hLayoutFooter, Layout<PERSON>ontent as hLayout<PERSON>ontent, Layout<PERSON>eader as hLayoutHeader, Layout as hLayout, MenuItem as hMenuItem, MenuItemGroup as hMenuItemGroup, SubMenu as hSubMenu, Menu as hMenu, Divider as hDivider, Space as hSpace, Button as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs, message } from 'ant-design-vue';
import { onMounted, ref } from 'vue';
import { UploadOutlined, UserOutlined, NotificationOutlined, AppstoreOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue';
import { themeColor } from '@haierbusiness-front/utils';

const props = defineProps({
    list: Object,
})

const selectedKeys = ref<string[]>([]);
const openKeys = ref<string[]>([]);

const clean = () => {
    selectedKeys.value = []
    openKeys.value = []
}
defineExpose({ clean });
const selectedKey = (param: any) => {

    selectedKeys.value = [param.id]
}
const openKey = (param: any[]) => {

    const opens = [] as Array<string>
    for(let open of param){
        opens.push(open.id)
    }
    openKeys.value = opens
}
</script>

<template>
    <h-menu v-model:selectedKeys="selectedKeys" v-model:openKeys="openKeys" :theme="themeColor.isDark" mode="inline" >
        <e-menu-item v-for="i of list" :item="i" @select-key="selectedKey" @open-key="openKey"></e-menu-item>
    </h-menu>
</template>
