<script setup lang="ts">
// 方案变更-保险产品
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  schemeChangeType: {
    // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看
    type: String,
    default: '',
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeInsurancesEmit']);

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const subtotal = ref<number>(0); // 小计

// 价格计算
const priceCalcFun = () => {
  const isAllPriceWrite = newSchemeList.value.every((e) => e.planPrice && e.planPrice > 0);
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      subtotal.value += e.planPrice;
    });

    emit('schemePriceEmit', { type: 'insurance', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  }
};

const schemePlanLabelList = ['参保人数', '保险产品', '单价'];

const changePrice = (index: number) => {
  if (newSchemeList.value[index].biddingPrice) {
    newSchemeList.value[index].planPrice =
      newSchemeList.value[index].biddingPrice * newSchemeList.value[index].schemePersonNum;
  }

  // 价格计算
  priceCalcFun();
};

// 暂存
const insuranceTempSave = () => {
  emit('schemeInsurancesEmit', {
    schemeInsurances: [...newSchemeList.value],
    schemeIndex: props.schemeIndex,
  });
};
// 校验
const insuranceSub = () => {
  let isVerPassed = true;

  // newSchemeList.value.forEach((e, i) => {
  //   if (e.schemeUnitPrice === null || e.schemeUnitPrice === undefined) {
  //     message.error('请填写' + e.demandDate + '保险' + (i + 1) + '单价');

  //     isVerPassed = false;
  //     return;
  //   }
  // });

  if (isVerPassed) {
    insuranceTempSave();
  }

  return isVerPassed;
};

defineExpose({ insuranceSub, insuranceTempSave });

onMounted(async () => {
  // console.log('%c [ 保险产品 ]-24', 'font-size:13px; background:pink; color:#bf2c9f;', props.schemeItem.insurances);

  oldSchemeList.value = JSON.parse(JSON.stringify(props.schemeItem))?.insurances || [];

  if (props.isSchemeCache && props.schemeItem) {
    // 缓存 - 反显
    newSchemeList.value = props.schemeItem?.insurances || [];

    // 价格计算
    priceCalcFun();
  } else {
    const demandData = JSON.parse(JSON.stringify(props.schemeItem))?.insurances || [];
    newSchemeList.value = demandData.map((e) => {
      return {
        miceDemandInsuranceId: e.id,

        demandDate: e.demandDate,
        schemePersonNum: e.personNum,

        insuranceContent: e.insuranceContent,
        insuranceName: e.insuranceName,
        productId: e.productId,
        productMerchantId: e.productMerchantId,

        description: e.description,

        demandUnitPrice: e.demandUnitPrice,
        schemeUnitPrice: e.demandUnitPrice,
      };
    });
  }

  // 小计
  subtotal.value = 0;
  newSchemeList.value.forEach((e) => {
    if (e.schemeUnitPrice && e.schemePersonNum) {
      subtotal.value += e.schemeUnitPrice * e.schemePersonNum;
    }
  });

  emit('schemePriceEmit', { type: 'insurance', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
});
</script>

<template>
  <!-- 保险产品 -->
  <div class="scheme_vehicle" v-if="oldSchemeList.length > 0 || newSchemeList.length > 0">
    <div class="common_table">
      <!-- 左侧 -->
      <div
        class="common_table_l"
        v-if="props.schemeChangeType !== 'notBidding' && props.schemeChangeType !== 'biddingView'"
      >
        <div class="scheme_plan_title" v-show="oldSchemeList.length > 0">
          <div class="scheme_plan_img mr10"></div>
          <span>保险方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '保险产品' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              {{ item.personNum ? item.personNum + '人' : '-' }}
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.insuranceName || '-' }}
                </template>
                {{ item.insuranceName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.demandUnitPrice ? item.demandUnitPrice + '元/人' : '-' }}
                </template>
                {{ item.demandUnitPrice ? item.demandUnitPrice + '元/人' : '-' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeUnitPrice && item.schemePersonNum
                    ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemePersonNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice && item.schemePersonNum">
                {{ item.schemePersonNum + '人*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title" v-show="newSchemeList.length > 0">
          <div class="scheme_plan_img mr10"></div>
          <span>保险方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '保险产品' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.insuranceName || '-' }}
                </template>
                {{ item.insuranceName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-' }}
                </template>
                {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeUnitPrice && item.schemePersonNum
                    ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemePersonNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice && item.schemePersonNum">
                {{ item.schemePersonNum + '人*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_insurance.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  :deep(.ant-input-number .ant-input-number-input) {
    height: 24px;
    padding: 0 5px;
    text-align: end;

    width: 84px;
    font-weight: 500;
    font-size: 14px;
    color: #1868db;
    text-align: right;
    border-bottom: 1px solid #4e5969;
  }

  .p0 {
    padding: 0 !important;
  }
}
</style>
