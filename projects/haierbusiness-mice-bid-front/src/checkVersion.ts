/**
 * <AUTHOR>
 * @since 2025/08/01 8:41
 */
import {Button, notification} from 'ant-design-vue';
import {h} from 'vue';

let checkVersionTimeId: any;
let currentVersion = 0;


/**
 * 自动检查版本
 */
export function autoCheckVersion() {
    const close = () => {
        window.location.reload()
    };

    // 添加唯一标识符用于关闭通知
    const key = `open${Date.now()}`;

    notification.open({
        message: '有新的版本！',
        description:
            '有新的版本，请点击更新，更新后当前页面内容！',
        btn: () =>
            h(
                Button,
                {
                    type: 'primary',
                    size: 'small',
                    onClick: () => close(),
                },
                {default: () => '立即更新'},
            ),
        key,
        duration: 0,
    });

    fetch('version.json')
        .then(response => response.json())
        .then((data) => {
            currentVersion = data.version
            setTimeout(() => {
                check();
                checkVersionTimeId = setInterval(() => {
                    check();
                }, 10000);
            }, 2000);
        })
}

/**
 * 自动检查版本
 */
export function check() {
    fetch(window.location.origin + window.location.pathname + 'version.json')
        .then(response => response.json())
        .then((data) => {
            let newVersion = data.version;
            if (newVersion !== currentVersion) {


                clearInterval(checkVersionTimeId);
            }
        }).catch(function (error) {
        console.log(error);
    });
}

