import { loadEnv } from 'vite';

import vue from '@vitejs/plugin-vue';
import path from 'path';
import { writeFileSync } from 'fs'; // 添加文件写入功能
const CWD = process.cwd();

export default ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);

  // 生成版本信息插件
  const generateVersionPlugin = {
    name: 'generate-version',
    apply: 'build', // 仅在构建时执行
    closeBundle() {
      // 生成版本文件
      const versionInfo = {
        version: Date.now(), // 使用时间戳作为版本号
        buildTime: new Date().toISOString(),
        mode: mode
      };

      writeFileSync(
          path.resolve(__dirname, 'dist/version.json'),
          JSON.stringify(versionInfo, null, 2)
      );

      console.log('Version file generated:', versionInfo);
    }
  };

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [vue(),generateVersionPlugin],
    build: {
      target: ['es2015'],
      sourcemap: true,
    },
    server: {
      port: 5182,
      host: true,
      proxy: {
        // 通用上传接口
        // "/hb/ai/api": {
        //   // target: "http://localhost:8080/hb",
        //    target: "https://businessmanagement-test.haier.net/hbweb/payman/hb",
        //   // target: "http://localhost:9219",
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/hb\/ai\/api/, ""),
        // },
        '/hb/gd': {
          target: 'https://restapi.amap.com/v5',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/hb/gd`), ''),
        },
        // 通用上传接口
        '/hb/common/api': {
          target: 'https://businessmanagement-test.haier.net/hbweb/index/hb',
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`/hb`), ''),
        },
        '/hb': {
          target: 'https://businessmanagement-test.haier.net/hbweb/index/hb',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/hb/, ''),
        },
        '/upload': {
          target: 'https://businessmanagement-test.haier.net/hbweb/upload',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/upload/, ''),
        },
      },
    },
    css: {},
  };
};
